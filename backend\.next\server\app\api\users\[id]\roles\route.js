"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/[id]/roles/route";
exports.ids = ["app/api/users/[id]/roles/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_workspaces_nsl_back_SrsrMan_backend_app_api_users_id_roles_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/users/[id]/roles/route.ts */ \"(rsc)/./app/api/users/[id]/roles/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/users/[id]/roles/route\",\n        pathname: \"/api/users/[id]/roles\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/[id]/roles/route\"\n    },\n    resolvedPagePath: \"D:\\\\workspaces\\\\nsl\\\\back\\\\SrsrMan\\\\backend\\\\app\\\\api\\\\users\\\\[id]\\\\roles\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_workspaces_nsl_back_SrsrMan_backend_app_api_users_id_roles_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/users/[id]/roles/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/users/[id]/roles/route.ts":
/*!*******************************************!*\
  !*** ./app/api/users/[id]/roles/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/validation */ \"(rsc)/./lib/validation.ts\");\n/* harmony import */ var joi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! joi */ \"(rsc)/./node_modules/joi/lib/index.js\");\n/* harmony import */ var joi__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(joi__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst assignRolesSchema = joi__WEBPACK_IMPORTED_MODULE_4___default().object({\n    role_ids: joi__WEBPACK_IMPORTED_MODULE_4___default().array().items(joi__WEBPACK_IMPORTED_MODULE_4___default().string().uuid()).min(0).required(),\n    replace_existing: joi__WEBPACK_IMPORTED_MODULE_4___default().boolean().default(true)\n});\nasync function getUserRolesHandler(request, context, currentUser) {\n    try {\n        const { id } = context.params;\n        // Verify user exists\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            select: {\n                id: true,\n                email: true,\n                fullName: true,\n                isActive: true\n            }\n        });\n        if (!user) {\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(null, \"User not found\", \"NOT_FOUND\"), {\n                status: 404\n            });\n        }\n        // Get user roles with permissions\n        const userRoles = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userRole.findMany({\n            where: {\n                userId: id\n            },\n            include: {\n                role: {\n                    include: {\n                        rolePermissions: {\n                            include: {\n                                permission: true\n                            }\n                        }\n                    }\n                }\n            },\n            orderBy: {\n                assignedAt: \"desc\"\n            }\n        });\n        const transformedRoles = userRoles.map((ur)=>({\n                id: ur.role.id,\n                name: ur.role.name,\n                description: ur.role.description,\n                is_system_role: ur.role.isSystemRole,\n                assigned_at: ur.assignedAt,\n                assigned_by: ur.assignedBy,\n                permissions: ur.role.rolePermissions.map((rp)=>({\n                        id: rp.permission.id,\n                        name: rp.permission.name,\n                        resource: rp.permission.resource,\n                        action: rp.permission.action\n                    }))\n            }));\n        // Get all permissions for this user (flattened)\n        const allPermissions = userRoles.flatMap((ur)=>ur.role.rolePermissions.map((rp)=>({\n                    id: rp.permission.id,\n                    name: rp.permission.name,\n                    resource: rp.permission.resource,\n                    action: rp.permission.action,\n                    from_role: ur.role.name\n                })));\n        // Remove duplicates\n        const uniquePermissions = allPermissions.filter((permission, index, self)=>index === self.findIndex((p)=>p.id === permission.id));\n        return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)({\n            user: {\n                id: user.id,\n                email: user.email,\n                full_name: user.fullName,\n                is_active: user.isActive\n            },\n            roles: transformedRoles,\n            permissions: uniquePermissions,\n            role_count: transformedRoles.length,\n            permission_count: uniquePermissions.length\n        }), {\n            status: 200,\n            headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n        });\n    } catch (error) {\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error, \"Failed to fetch user roles\");\n    }\n}\nasync function assignUserRolesHandler(request, context, currentUser) {\n    try {\n        const { id } = context.params;\n        const body = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getRequestBody)(request);\n        const validation = (0,_lib_validation__WEBPACK_IMPORTED_MODULE_3__.validateRequest)(assignRolesSchema, body);\n        if (!validation.isValid) {\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(null, \"Validation failed\", \"VALIDATION_ERROR\"), {\n                status: 400\n            });\n        }\n        const { role_ids, replace_existing } = validation.data;\n        // Verify user exists\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            }\n        });\n        if (!user) {\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(null, \"User not found\", \"NOT_FOUND\"), {\n                status: 404\n            });\n        }\n        // Verify roles exist\n        const existingRoles = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.role.findMany({\n            where: {\n                id: {\n                    in: role_ids\n                }\n            }\n        });\n        if (existingRoles.length !== role_ids.length) {\n            const missingRoles = role_ids.filter((roleId)=>!existingRoles.find((r)=>r.id === roleId));\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(null, `Some roles not found: ${missingRoles.join(\", \")}`, \"INVALID_ROLES\"), {\n                status: 400\n            });\n        }\n        // Handle role assignment\n        if (replace_existing) {\n            // Remove all existing roles\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userRole.deleteMany({\n                where: {\n                    userId: id\n                }\n            });\n        }\n        // Add new roles (skip duplicates if not replacing)\n        if (role_ids.length > 0) {\n            const userRoleData = role_ids.map((roleId)=>({\n                    userId: id,\n                    roleId: roleId,\n                    assignedBy: currentUser.id\n                }));\n            if (replace_existing) {\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userRole.createMany({\n                    data: userRoleData\n                });\n            } else {\n                // Check for existing assignments to avoid duplicates\n                const existingAssignments = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userRole.findMany({\n                    where: {\n                        userId: id,\n                        roleId: {\n                            in: role_ids\n                        }\n                    }\n                });\n                const existingRoleIds = existingAssignments.map((ua)=>ua.roleId);\n                const newRoleIds = role_ids.filter((roleId)=>!existingRoleIds.includes(roleId));\n                if (newRoleIds.length > 0) {\n                    const newUserRoleData = newRoleIds.map((roleId)=>({\n                            userId: id,\n                            roleId: roleId,\n                            assignedBy: currentUser.id\n                        }));\n                    await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userRole.createMany({\n                        data: newUserRoleData\n                    });\n                }\n            }\n        }\n        // Fetch updated user with roles\n        const userWithRoles = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                userRoles: {\n                    include: {\n                        role: {\n                            include: {\n                                rolePermissions: {\n                                    include: {\n                                        permission: true\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        const transformedRoles = userWithRoles.userRoles.map((ur)=>({\n                id: ur.role.id,\n                name: ur.role.name,\n                description: ur.role.description,\n                assigned_at: ur.assignedAt\n            }));\n        return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)({\n            message: \"User roles updated successfully\",\n            user: {\n                id: userWithRoles.id,\n                email: userWithRoles.email,\n                full_name: userWithRoles.fullName,\n                roles: transformedRoles\n            },\n            operation: replace_existing ? \"replaced\" : \"added\",\n            roles_assigned: role_ids.length\n        }), {\n            status: 200,\n            headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n        });\n    } catch (error) {\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error, \"Failed to assign user roles\");\n    }\n}\nasync function removeUserRoleHandler(request, context, currentUser) {\n    try {\n        const { id } = context.params;\n        const body = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getRequestBody)(request);\n        const removeRoleSchema = joi__WEBPACK_IMPORTED_MODULE_4___default().object({\n            role_id: joi__WEBPACK_IMPORTED_MODULE_4___default().string().uuid().required()\n        });\n        const validation = (0,_lib_validation__WEBPACK_IMPORTED_MODULE_3__.validateRequest)(removeRoleSchema, body);\n        if (!validation.isValid) {\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(null, \"Validation failed\", \"VALIDATION_ERROR\"), {\n                status: 400\n            });\n        }\n        const { role_id } = validation.data;\n        // Verify user exists\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            }\n        });\n        if (!user) {\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(null, \"User not found\", \"NOT_FOUND\"), {\n                status: 404\n            });\n        }\n        // Check if user has this role\n        const userRole = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userRole.findFirst({\n            where: {\n                userId: id,\n                roleId: role_id\n            },\n            include: {\n                role: true\n            }\n        });\n        if (!userRole) {\n            return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)(null, \"User does not have this role\", \"ROLE_NOT_ASSIGNED\"), {\n                status: 404\n            });\n        }\n        // Remove the role\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userRole.delete({\n            where: {\n                id: userRole.id\n            }\n        });\n        return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)({\n            message: \"Role removed from user successfully\",\n            removed_role: {\n                id: userRole.role.id,\n                name: userRole.role.name\n            }\n        }), {\n            status: 200,\n            headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n        });\n    } catch (error) {\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error, \"Failed to remove user role\");\n    }\n}\nconst GET = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.requireAuth)(getUserRolesHandler);\nconst POST = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.requireRole)([\n    \"admin\"\n])(assignUserRolesHandler);\nconst DELETE = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.requireRole)([\n    \"admin\"\n])(removeUserRoleHandler);\nasync function OPTIONS() {\n    return new Response(null, {\n        status: 200,\n        headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3VzZXJzL1tpZF0vcm9sZXMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUNzQztBQUNnQjtBQUNvQztBQUN2QztBQUM3QjtBQUV0QixNQUFNUyxvQkFBb0JELGlEQUFVLENBQUM7SUFDbkNHLFVBQVVILGdEQUFTLEdBQUdLLEtBQUssQ0FBQ0wsaURBQVUsR0FBR08sSUFBSSxJQUFJQyxHQUFHLENBQUMsR0FBR0MsUUFBUTtJQUNoRUMsa0JBQWtCVixrREFBVyxHQUFHWSxPQUFPLENBQUM7QUFDMUM7QUFFQSxlQUFlQyxvQkFBb0JDLE9BQW9CLEVBQUVDLE9BQW1DLEVBQUVDLFdBQWdCO0lBQzVHLElBQUk7UUFDRixNQUFNLEVBQUVDLEVBQUUsRUFBRSxHQUFHRixRQUFRRyxNQUFNO1FBRTdCLHFCQUFxQjtRQUNyQixNQUFNQyxPQUFPLE1BQU0zQiwrQ0FBTUEsQ0FBQzJCLElBQUksQ0FBQ0MsVUFBVSxDQUFDO1lBQ3hDQyxPQUFPO2dCQUFFSjtZQUFHO1lBQ1pLLFFBQVE7Z0JBQ05MLElBQUk7Z0JBQ0pNLE9BQU87Z0JBQ1BDLFVBQVU7Z0JBQ1ZDLFVBQVU7WUFDWjtRQUNGO1FBRUEsSUFBSSxDQUFDTixNQUFNO1lBQ1QsT0FBT08sU0FBU0MsSUFBSSxDQUNsQmhDLDZEQUFpQkEsQ0FBQyxNQUFNLGtCQUFrQixjQUMxQztnQkFBRWlDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLGtDQUFrQztRQUNsQyxNQUFNQyxZQUFZLE1BQU1yQywrQ0FBTUEsQ0FBQ3NDLFFBQVEsQ0FBQ0MsUUFBUSxDQUFDO1lBQy9DVixPQUFPO2dCQUFFVyxRQUFRZjtZQUFHO1lBQ3BCZ0IsU0FBUztnQkFDUEMsTUFBTTtvQkFDSkQsU0FBUzt3QkFDUEUsaUJBQWlCOzRCQUNmRixTQUFTO2dDQUNQRyxZQUFZOzRCQUNkO3dCQUNGO29CQUNGO2dCQUNGO1lBQ0Y7WUFDQUMsU0FBUztnQkFDUEMsWUFBWTtZQUNkO1FBQ0Y7UUFFQSxNQUFNQyxtQkFBbUJWLFVBQVVXLEdBQUcsQ0FBQ0MsQ0FBQUEsS0FBTztnQkFDNUN4QixJQUFJd0IsR0FBR1AsSUFBSSxDQUFDakIsRUFBRTtnQkFDZHlCLE1BQU1ELEdBQUdQLElBQUksQ0FBQ1EsSUFBSTtnQkFDbEJDLGFBQWFGLEdBQUdQLElBQUksQ0FBQ1MsV0FBVztnQkFDaENDLGdCQUFnQkgsR0FBR1AsSUFBSSxDQUFDVyxZQUFZO2dCQUNwQ0MsYUFBYUwsR0FBR0gsVUFBVTtnQkFDMUJTLGFBQWFOLEdBQUdPLFVBQVU7Z0JBQzFCQyxhQUFhUixHQUFHUCxJQUFJLENBQUNDLGVBQWUsQ0FBQ0ssR0FBRyxDQUFDVSxDQUFBQSxLQUFPO3dCQUM5Q2pDLElBQUlpQyxHQUFHZCxVQUFVLENBQUNuQixFQUFFO3dCQUNwQnlCLE1BQU1RLEdBQUdkLFVBQVUsQ0FBQ00sSUFBSTt3QkFDeEJTLFVBQVVELEdBQUdkLFVBQVUsQ0FBQ2UsUUFBUTt3QkFDaENDLFFBQVFGLEdBQUdkLFVBQVUsQ0FBQ2dCLE1BQU07b0JBQzlCO1lBQ0Y7UUFFQSxnREFBZ0Q7UUFDaEQsTUFBTUMsaUJBQWlCeEIsVUFBVXlCLE9BQU8sQ0FBQ2IsQ0FBQUEsS0FDdkNBLEdBQUdQLElBQUksQ0FBQ0MsZUFBZSxDQUFDSyxHQUFHLENBQUNVLENBQUFBLEtBQU87b0JBQ2pDakMsSUFBSWlDLEdBQUdkLFVBQVUsQ0FBQ25CLEVBQUU7b0JBQ3BCeUIsTUFBTVEsR0FBR2QsVUFBVSxDQUFDTSxJQUFJO29CQUN4QlMsVUFBVUQsR0FBR2QsVUFBVSxDQUFDZSxRQUFRO29CQUNoQ0MsUUFBUUYsR0FBR2QsVUFBVSxDQUFDZ0IsTUFBTTtvQkFDNUJHLFdBQVdkLEdBQUdQLElBQUksQ0FBQ1EsSUFBSTtnQkFDekI7UUFHRixvQkFBb0I7UUFDcEIsTUFBTWMsb0JBQW9CSCxlQUFlSSxNQUFNLENBQUMsQ0FBQ3JCLFlBQVlzQixPQUFPQyxPQUNsRUQsVUFBVUMsS0FBS0MsU0FBUyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFNUMsRUFBRSxLQUFLbUIsV0FBV25CLEVBQUU7UUFHdEQsT0FBT1MsU0FBU0MsSUFBSSxDQUNsQmhDLDZEQUFpQkEsQ0FBQztZQUNoQndCLE1BQU07Z0JBQ0pGLElBQUlFLEtBQUtGLEVBQUU7Z0JBQ1hNLE9BQU9KLEtBQUtJLEtBQUs7Z0JBQ2pCdUMsV0FBVzNDLEtBQUtLLFFBQVE7Z0JBQ3hCdUMsV0FBVzVDLEtBQUtNLFFBQVE7WUFDMUI7WUFDQXVDLE9BQU96QjtZQUNQVSxhQUFhTztZQUNiUyxZQUFZMUIsaUJBQWlCMkIsTUFBTTtZQUNuQ0Msa0JBQWtCWCxrQkFBa0JVLE1BQU07UUFDNUMsSUFDQTtZQUNFdEMsUUFBUTtZQUNSd0MsU0FBU3RFLHVEQUFXQTtRQUN0QjtJQUVKLEVBQUUsT0FBT3VFLE9BQU87UUFDZCxPQUFPeEUsdURBQVdBLENBQUN3RSxPQUFPO0lBQzVCO0FBQ0Y7QUFFQSxlQUFlQyx1QkFBdUJ4RCxPQUFvQixFQUFFQyxPQUFtQyxFQUFFQyxXQUFnQjtJQUMvRyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxFQUFFLEVBQUUsR0FBR0YsUUFBUUcsTUFBTTtRQUM3QixNQUFNcUQsT0FBTyxNQUFNM0UsMERBQWNBLENBQUNrQjtRQUVsQyxNQUFNMEQsYUFBYXpFLGdFQUFlQSxDQUFDRSxtQkFBbUJzRTtRQUN0RCxJQUFJLENBQUNDLFdBQVdDLE9BQU8sRUFBRTtZQUN2QixPQUFPL0MsU0FBU0MsSUFBSSxDQUNsQmhDLDZEQUFpQkEsQ0FBQyxNQUFNLHFCQUFxQixxQkFDN0M7Z0JBQUVpQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNLEVBQUV6QixRQUFRLEVBQUVPLGdCQUFnQixFQUFFLEdBQUc4RCxXQUFXRSxJQUFJO1FBRXRELHFCQUFxQjtRQUNyQixNQUFNdkQsT0FBTyxNQUFNM0IsK0NBQU1BLENBQUMyQixJQUFJLENBQUNDLFVBQVUsQ0FBQztZQUN4Q0MsT0FBTztnQkFBRUo7WUFBRztRQUNkO1FBRUEsSUFBSSxDQUFDRSxNQUFNO1lBQ1QsT0FBT08sU0FBU0MsSUFBSSxDQUNsQmhDLDZEQUFpQkEsQ0FBQyxNQUFNLGtCQUFrQixjQUMxQztnQkFBRWlDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLHFCQUFxQjtRQUNyQixNQUFNK0MsZ0JBQWdCLE1BQU1uRiwrQ0FBTUEsQ0FBQzBDLElBQUksQ0FBQ0gsUUFBUSxDQUFDO1lBQy9DVixPQUFPO2dCQUNMSixJQUFJO29CQUFFMkQsSUFBSXpFO2dCQUFTO1lBQ3JCO1FBQ0Y7UUFFQSxJQUFJd0UsY0FBY1QsTUFBTSxLQUFLL0QsU0FBUytELE1BQU0sRUFBRTtZQUM1QyxNQUFNVyxlQUFlMUUsU0FBU3NELE1BQU0sQ0FBQ3FCLENBQUFBLFNBQVUsQ0FBQ0gsY0FBY0ksSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFL0QsRUFBRSxLQUFLNkQ7WUFDakYsT0FBT3BELFNBQVNDLElBQUksQ0FDbEJoQyw2REFBaUJBLENBQUMsTUFBTSxDQUFDLHNCQUFzQixFQUFFa0YsYUFBYUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxFQUFFLGtCQUM1RTtnQkFBRXJELFFBQVE7WUFBSTtRQUVsQjtRQUVBLHlCQUF5QjtRQUN6QixJQUFJbEIsa0JBQWtCO1lBQ3BCLDRCQUE0QjtZQUM1QixNQUFNbEIsK0NBQU1BLENBQUNzQyxRQUFRLENBQUNvRCxVQUFVLENBQUM7Z0JBQy9CN0QsT0FBTztvQkFBRVcsUUFBUWY7Z0JBQUc7WUFDdEI7UUFDRjtRQUVBLG1EQUFtRDtRQUNuRCxJQUFJZCxTQUFTK0QsTUFBTSxHQUFHLEdBQUc7WUFDdkIsTUFBTWlCLGVBQWVoRixTQUFTcUMsR0FBRyxDQUFDLENBQUNzQyxTQUFvQjtvQkFDckQ5QyxRQUFRZjtvQkFDUjZELFFBQVFBO29CQUNSOUIsWUFBWWhDLFlBQVlDLEVBQUU7Z0JBQzVCO1lBRUEsSUFBSVAsa0JBQWtCO2dCQUNwQixNQUFNbEIsK0NBQU1BLENBQUNzQyxRQUFRLENBQUNzRCxVQUFVLENBQUM7b0JBQy9CVixNQUFNUztnQkFDUjtZQUNGLE9BQU87Z0JBQ0wscURBQXFEO2dCQUNyRCxNQUFNRSxzQkFBc0IsTUFBTTdGLCtDQUFNQSxDQUFDc0MsUUFBUSxDQUFDQyxRQUFRLENBQUM7b0JBQ3pEVixPQUFPO3dCQUNMVyxRQUFRZjt3QkFDUjZELFFBQVE7NEJBQUVGLElBQUl6RTt3QkFBUztvQkFDekI7Z0JBQ0Y7Z0JBRUEsTUFBTW1GLGtCQUFrQkQsb0JBQW9CN0MsR0FBRyxDQUFDK0MsQ0FBQUEsS0FBTUEsR0FBR1QsTUFBTTtnQkFDL0QsTUFBTVUsYUFBYXJGLFNBQVNzRCxNQUFNLENBQUMsQ0FBQ3FCLFNBQW1CLENBQUNRLGdCQUFnQkcsUUFBUSxDQUFDWDtnQkFFakYsSUFBSVUsV0FBV3RCLE1BQU0sR0FBRyxHQUFHO29CQUN6QixNQUFNd0Isa0JBQWtCRixXQUFXaEQsR0FBRyxDQUFDLENBQUNzQyxTQUFvQjs0QkFDMUQ5QyxRQUFRZjs0QkFDUjZELFFBQVFBOzRCQUNSOUIsWUFBWWhDLFlBQVlDLEVBQUU7d0JBQzVCO29CQUVBLE1BQU16QiwrQ0FBTUEsQ0FBQ3NDLFFBQVEsQ0FBQ3NELFVBQVUsQ0FBQzt3QkFDL0JWLE1BQU1nQjtvQkFDUjtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSxnQ0FBZ0M7UUFDaEMsTUFBTUMsZ0JBQWdCLE1BQU1uRywrQ0FBTUEsQ0FBQzJCLElBQUksQ0FBQ0MsVUFBVSxDQUFDO1lBQ2pEQyxPQUFPO2dCQUFFSjtZQUFHO1lBQ1pnQixTQUFTO2dCQUNQSixXQUFXO29CQUNUSSxTQUFTO3dCQUNQQyxNQUFNOzRCQUNKRCxTQUFTO2dDQUNQRSxpQkFBaUI7b0NBQ2ZGLFNBQVM7d0NBQ1BHLFlBQVk7b0NBQ2Q7Z0NBQ0Y7NEJBQ0Y7d0JBQ0Y7b0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsTUFBTUcsbUJBQW1Cb0QsY0FBZTlELFNBQVMsQ0FBQ1csR0FBRyxDQUFDQyxDQUFBQSxLQUFPO2dCQUMzRHhCLElBQUl3QixHQUFHUCxJQUFJLENBQUNqQixFQUFFO2dCQUNkeUIsTUFBTUQsR0FBR1AsSUFBSSxDQUFDUSxJQUFJO2dCQUNsQkMsYUFBYUYsR0FBR1AsSUFBSSxDQUFDUyxXQUFXO2dCQUNoQ0csYUFBYUwsR0FBR0gsVUFBVTtZQUM1QjtRQUVBLE9BQU9aLFNBQVNDLElBQUksQ0FDbEJoQyw2REFBaUJBLENBQUM7WUFDaEJpRyxTQUFTO1lBQ1R6RSxNQUFNO2dCQUNKRixJQUFJMEUsY0FBZTFFLEVBQUU7Z0JBQ3JCTSxPQUFPb0UsY0FBZXBFLEtBQUs7Z0JBQzNCdUMsV0FBVzZCLGNBQWVuRSxRQUFRO2dCQUNsQ3dDLE9BQU96QjtZQUNUO1lBQ0FzRCxXQUFXbkYsbUJBQW1CLGFBQWE7WUFDM0NvRixnQkFBZ0IzRixTQUFTK0QsTUFBTTtRQUNqQyxJQUNBO1lBQ0V0QyxRQUFRO1lBQ1J3QyxTQUFTdEUsdURBQVdBO1FBQ3RCO0lBRUosRUFBRSxPQUFPdUUsT0FBTztRQUNkLE9BQU94RSx1REFBV0EsQ0FBQ3dFLE9BQU87SUFDNUI7QUFDRjtBQUVBLGVBQWUwQixzQkFBc0JqRixPQUFvQixFQUFFQyxPQUFtQyxFQUFFQyxXQUFnQjtJQUM5RyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxFQUFFLEVBQUUsR0FBR0YsUUFBUUcsTUFBTTtRQUM3QixNQUFNcUQsT0FBTyxNQUFNM0UsMERBQWNBLENBQUNrQjtRQUVsQyxNQUFNa0YsbUJBQW1CaEcsaURBQVUsQ0FBQztZQUNsQ2lHLFNBQVNqRyxpREFBVSxHQUFHTyxJQUFJLEdBQUdFLFFBQVE7UUFDdkM7UUFFQSxNQUFNK0QsYUFBYXpFLGdFQUFlQSxDQUFDaUcsa0JBQWtCekI7UUFDckQsSUFBSSxDQUFDQyxXQUFXQyxPQUFPLEVBQUU7WUFDdkIsT0FBTy9DLFNBQVNDLElBQUksQ0FDbEJoQyw2REFBaUJBLENBQUMsTUFBTSxxQkFBcUIscUJBQzdDO2dCQUFFaUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsTUFBTSxFQUFFcUUsT0FBTyxFQUFFLEdBQUd6QixXQUFXRSxJQUFJO1FBRW5DLHFCQUFxQjtRQUNyQixNQUFNdkQsT0FBTyxNQUFNM0IsK0NBQU1BLENBQUMyQixJQUFJLENBQUNDLFVBQVUsQ0FBQztZQUN4Q0MsT0FBTztnQkFBRUo7WUFBRztRQUNkO1FBRUEsSUFBSSxDQUFDRSxNQUFNO1lBQ1QsT0FBT08sU0FBU0MsSUFBSSxDQUNsQmhDLDZEQUFpQkEsQ0FBQyxNQUFNLGtCQUFrQixjQUMxQztnQkFBRWlDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLDhCQUE4QjtRQUM5QixNQUFNRSxXQUFXLE1BQU10QywrQ0FBTUEsQ0FBQ3NDLFFBQVEsQ0FBQ29FLFNBQVMsQ0FBQztZQUMvQzdFLE9BQU87Z0JBQ0xXLFFBQVFmO2dCQUNSNkQsUUFBUW1CO1lBQ1Y7WUFDQWhFLFNBQVM7Z0JBQ1BDLE1BQU07WUFDUjtRQUNGO1FBRUEsSUFBSSxDQUFDSixVQUFVO1lBQ2IsT0FBT0osU0FBU0MsSUFBSSxDQUNsQmhDLDZEQUFpQkEsQ0FBQyxNQUFNLGdDQUFnQyxzQkFDeEQ7Z0JBQUVpQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxrQkFBa0I7UUFDbEIsTUFBTXBDLCtDQUFNQSxDQUFDc0MsUUFBUSxDQUFDcUUsTUFBTSxDQUFDO1lBQzNCOUUsT0FBTztnQkFDTEosSUFBSWEsU0FBU2IsRUFBRTtZQUNqQjtRQUNGO1FBRUEsT0FBT1MsU0FBU0MsSUFBSSxDQUNsQmhDLDZEQUFpQkEsQ0FBQztZQUNoQmlHLFNBQVM7WUFDVFEsY0FBYztnQkFDWm5GLElBQUlhLFNBQVNJLElBQUksQ0FBQ2pCLEVBQUU7Z0JBQ3BCeUIsTUFBTVosU0FBU0ksSUFBSSxDQUFDUSxJQUFJO1lBQzFCO1FBQ0YsSUFDQTtZQUNFZCxRQUFRO1lBQ1J3QyxTQUFTdEUsdURBQVdBO1FBQ3RCO0lBRUosRUFBRSxPQUFPdUUsT0FBTztRQUNkLE9BQU94RSx1REFBV0EsQ0FBQ3dFLE9BQU87SUFDNUI7QUFDRjtBQUVPLE1BQU1nQyxNQUFNNUcsc0RBQVdBLENBQUNvQixxQkFBcUI7QUFDN0MsTUFBTXlGLE9BQU81RyxzREFBV0EsQ0FBQztJQUFDO0NBQVEsRUFBRTRFLHdCQUF3QjtBQUM1RCxNQUFNaUMsU0FBUzdHLHNEQUFXQSxDQUFDO0lBQUM7Q0FBUSxFQUFFcUcsdUJBQXVCO0FBRTdELGVBQWVTO0lBQ3BCLE9BQU8sSUFBSTlFLFNBQVMsTUFBTTtRQUN4QkUsUUFBUTtRQUNSd0MsU0FBU3RFLHVEQUFXQTtJQUN0QjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3Jzci1wcm9wZXJ0eS1tYW5hZ2VtZW50LWJhY2tlbmQvLi9hcHAvYXBpL3VzZXJzL1tpZF0vcm9sZXMvcm91dGUudHM/MmVjNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IHByaXNtYSB9IGZyb20gJ0AvbGliL3ByaXNtYSc7XG5pbXBvcnQgeyByZXF1aXJlQXV0aCwgcmVxdWlyZVJvbGUgfSBmcm9tICdAL2xpYi9hdXRoJztcbmltcG9ydCB7IGNyZWF0ZUFwaVJlc3BvbnNlLCBnZXRSZXF1ZXN0Qm9keSwgaGFuZGxlRXJyb3IsIGNvcnNIZWFkZXJzIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuaW1wb3J0IHsgdmFsaWRhdGVSZXF1ZXN0IH0gZnJvbSAnQC9saWIvdmFsaWRhdGlvbic7XG5pbXBvcnQgSm9pIGZyb20gJ2pvaSc7XG5cbmNvbnN0IGFzc2lnblJvbGVzU2NoZW1hID0gSm9pLm9iamVjdCh7XG4gIHJvbGVfaWRzOiBKb2kuYXJyYXkoKS5pdGVtcyhKb2kuc3RyaW5nKCkudXVpZCgpKS5taW4oMCkucmVxdWlyZWQoKSxcbiAgcmVwbGFjZV9leGlzdGluZzogSm9pLmJvb2xlYW4oKS5kZWZhdWx0KHRydWUpLFxufSk7XG5cbmFzeW5jIGZ1bmN0aW9uIGdldFVzZXJSb2xlc0hhbmRsZXIocmVxdWVzdDogTmV4dFJlcXVlc3QsIGNvbnRleHQ6IHsgcGFyYW1zOiB7IGlkOiBzdHJpbmcgfSB9LCBjdXJyZW50VXNlcjogYW55KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBpZCB9ID0gY29udGV4dC5wYXJhbXM7XG5cbiAgICAvLyBWZXJpZnkgdXNlciBleGlzdHNcbiAgICBjb25zdCB1c2VyID0gYXdhaXQgcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XG4gICAgICB3aGVyZTogeyBpZCB9LFxuICAgICAgc2VsZWN0OiB7XG4gICAgICAgIGlkOiB0cnVlLFxuICAgICAgICBlbWFpbDogdHJ1ZSxcbiAgICAgICAgZnVsbE5hbWU6IHRydWUsXG4gICAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmICghdXNlcikge1xuICAgICAgcmV0dXJuIFJlc3BvbnNlLmpzb24oXG4gICAgICAgIGNyZWF0ZUFwaVJlc3BvbnNlKG51bGwsICdVc2VyIG5vdCBmb3VuZCcsICdOT1RfRk9VTkQnKSxcbiAgICAgICAgeyBzdGF0dXM6IDQwNCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIEdldCB1c2VyIHJvbGVzIHdpdGggcGVybWlzc2lvbnNcbiAgICBjb25zdCB1c2VyUm9sZXMgPSBhd2FpdCBwcmlzbWEudXNlclJvbGUuZmluZE1hbnkoe1xuICAgICAgd2hlcmU6IHsgdXNlcklkOiBpZCB9LFxuICAgICAgaW5jbHVkZToge1xuICAgICAgICByb2xlOiB7XG4gICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgcm9sZVBlcm1pc3Npb25zOiB7XG4gICAgICAgICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICAgICAgICBwZXJtaXNzaW9uOiB0cnVlLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICAgIG9yZGVyQnk6IHtcbiAgICAgICAgYXNzaWduZWRBdDogJ2Rlc2MnLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGNvbnN0IHRyYW5zZm9ybWVkUm9sZXMgPSB1c2VyUm9sZXMubWFwKHVyID0+ICh7XG4gICAgICBpZDogdXIucm9sZS5pZCxcbiAgICAgIG5hbWU6IHVyLnJvbGUubmFtZSxcbiAgICAgIGRlc2NyaXB0aW9uOiB1ci5yb2xlLmRlc2NyaXB0aW9uLFxuICAgICAgaXNfc3lzdGVtX3JvbGU6IHVyLnJvbGUuaXNTeXN0ZW1Sb2xlLFxuICAgICAgYXNzaWduZWRfYXQ6IHVyLmFzc2lnbmVkQXQsXG4gICAgICBhc3NpZ25lZF9ieTogdXIuYXNzaWduZWRCeSxcbiAgICAgIHBlcm1pc3Npb25zOiB1ci5yb2xlLnJvbGVQZXJtaXNzaW9ucy5tYXAocnAgPT4gKHtcbiAgICAgICAgaWQ6IHJwLnBlcm1pc3Npb24uaWQsXG4gICAgICAgIG5hbWU6IHJwLnBlcm1pc3Npb24ubmFtZSxcbiAgICAgICAgcmVzb3VyY2U6IHJwLnBlcm1pc3Npb24ucmVzb3VyY2UsXG4gICAgICAgIGFjdGlvbjogcnAucGVybWlzc2lvbi5hY3Rpb24sXG4gICAgICB9KSksXG4gICAgfSkpO1xuXG4gICAgLy8gR2V0IGFsbCBwZXJtaXNzaW9ucyBmb3IgdGhpcyB1c2VyIChmbGF0dGVuZWQpXG4gICAgY29uc3QgYWxsUGVybWlzc2lvbnMgPSB1c2VyUm9sZXMuZmxhdE1hcCh1ciA9PiBcbiAgICAgIHVyLnJvbGUucm9sZVBlcm1pc3Npb25zLm1hcChycCA9PiAoe1xuICAgICAgICBpZDogcnAucGVybWlzc2lvbi5pZCxcbiAgICAgICAgbmFtZTogcnAucGVybWlzc2lvbi5uYW1lLFxuICAgICAgICByZXNvdXJjZTogcnAucGVybWlzc2lvbi5yZXNvdXJjZSxcbiAgICAgICAgYWN0aW9uOiBycC5wZXJtaXNzaW9uLmFjdGlvbixcbiAgICAgICAgZnJvbV9yb2xlOiB1ci5yb2xlLm5hbWUsXG4gICAgICB9KSlcbiAgICApO1xuXG4gICAgLy8gUmVtb3ZlIGR1cGxpY2F0ZXNcbiAgICBjb25zdCB1bmlxdWVQZXJtaXNzaW9ucyA9IGFsbFBlcm1pc3Npb25zLmZpbHRlcigocGVybWlzc2lvbiwgaW5kZXgsIHNlbGYpID0+XG4gICAgICBpbmRleCA9PT0gc2VsZi5maW5kSW5kZXgocCA9PiBwLmlkID09PSBwZXJtaXNzaW9uLmlkKVxuICAgICk7XG5cbiAgICByZXR1cm4gUmVzcG9uc2UuanNvbihcbiAgICAgIGNyZWF0ZUFwaVJlc3BvbnNlKHtcbiAgICAgICAgdXNlcjoge1xuICAgICAgICAgIGlkOiB1c2VyLmlkLFxuICAgICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxuICAgICAgICAgIGZ1bGxfbmFtZTogdXNlci5mdWxsTmFtZSxcbiAgICAgICAgICBpc19hY3RpdmU6IHVzZXIuaXNBY3RpdmUsXG4gICAgICAgIH0sXG4gICAgICAgIHJvbGVzOiB0cmFuc2Zvcm1lZFJvbGVzLFxuICAgICAgICBwZXJtaXNzaW9uczogdW5pcXVlUGVybWlzc2lvbnMsXG4gICAgICAgIHJvbGVfY291bnQ6IHRyYW5zZm9ybWVkUm9sZXMubGVuZ3RoLFxuICAgICAgICBwZXJtaXNzaW9uX2NvdW50OiB1bmlxdWVQZXJtaXNzaW9ucy5sZW5ndGgsXG4gICAgICB9KSxcbiAgICAgIHsgXG4gICAgICAgIHN0YXR1czogMjAwLFxuICAgICAgICBoZWFkZXJzOiBjb3JzSGVhZGVycygpLFxuICAgICAgfVxuICAgICk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIGhhbmRsZUVycm9yKGVycm9yLCAnRmFpbGVkIHRvIGZldGNoIHVzZXIgcm9sZXMnKTtcbiAgfVxufVxuXG5hc3luYyBmdW5jdGlvbiBhc3NpZ25Vc2VyUm9sZXNIYW5kbGVyKHJlcXVlc3Q6IE5leHRSZXF1ZXN0LCBjb250ZXh0OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfSwgY3VycmVudFVzZXI6IGFueSkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgaWQgfSA9IGNvbnRleHQucGFyYW1zO1xuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCBnZXRSZXF1ZXN0Qm9keShyZXF1ZXN0KTtcbiAgICBcbiAgICBjb25zdCB2YWxpZGF0aW9uID0gdmFsaWRhdGVSZXF1ZXN0KGFzc2lnblJvbGVzU2NoZW1hLCBib2R5KTtcbiAgICBpZiAoIXZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgICAgcmV0dXJuIFJlc3BvbnNlLmpzb24oXG4gICAgICAgIGNyZWF0ZUFwaVJlc3BvbnNlKG51bGwsICdWYWxpZGF0aW9uIGZhaWxlZCcsICdWQUxJREFUSU9OX0VSUk9SJyksXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCB7IHJvbGVfaWRzLCByZXBsYWNlX2V4aXN0aW5nIH0gPSB2YWxpZGF0aW9uLmRhdGE7XG5cbiAgICAvLyBWZXJpZnkgdXNlciBleGlzdHNcbiAgICBjb25zdCB1c2VyID0gYXdhaXQgcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XG4gICAgICB3aGVyZTogeyBpZCB9LFxuICAgIH0pO1xuXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICByZXR1cm4gUmVzcG9uc2UuanNvbihcbiAgICAgICAgY3JlYXRlQXBpUmVzcG9uc2UobnVsbCwgJ1VzZXIgbm90IGZvdW5kJywgJ05PVF9GT1VORCcpLFxuICAgICAgICB7IHN0YXR1czogNDA0IH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gVmVyaWZ5IHJvbGVzIGV4aXN0XG4gICAgY29uc3QgZXhpc3RpbmdSb2xlcyA9IGF3YWl0IHByaXNtYS5yb2xlLmZpbmRNYW55KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIGlkOiB7IGluOiByb2xlX2lkcyB9LFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmIChleGlzdGluZ1JvbGVzLmxlbmd0aCAhPT0gcm9sZV9pZHMubGVuZ3RoKSB7XG4gICAgICBjb25zdCBtaXNzaW5nUm9sZXMgPSByb2xlX2lkcy5maWx0ZXIocm9sZUlkID0+ICFleGlzdGluZ1JvbGVzLmZpbmQociA9PiByLmlkID09PSByb2xlSWQpKTtcbiAgICAgIHJldHVybiBSZXNwb25zZS5qc29uKFxuICAgICAgICBjcmVhdGVBcGlSZXNwb25zZShudWxsLCBgU29tZSByb2xlcyBub3QgZm91bmQ6ICR7bWlzc2luZ1JvbGVzLmpvaW4oJywgJyl9YCwgJ0lOVkFMSURfUk9MRVMnKSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIEhhbmRsZSByb2xlIGFzc2lnbm1lbnRcbiAgICBpZiAocmVwbGFjZV9leGlzdGluZykge1xuICAgICAgLy8gUmVtb3ZlIGFsbCBleGlzdGluZyByb2xlc1xuICAgICAgYXdhaXQgcHJpc21hLnVzZXJSb2xlLmRlbGV0ZU1hbnkoe1xuICAgICAgICB3aGVyZTogeyB1c2VySWQ6IGlkIH0sXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBBZGQgbmV3IHJvbGVzIChza2lwIGR1cGxpY2F0ZXMgaWYgbm90IHJlcGxhY2luZylcbiAgICBpZiAocm9sZV9pZHMubGVuZ3RoID4gMCkge1xuICAgICAgY29uc3QgdXNlclJvbGVEYXRhID0gcm9sZV9pZHMubWFwKChyb2xlSWQ6IHN0cmluZykgPT4gKHtcbiAgICAgICAgdXNlcklkOiBpZCxcbiAgICAgICAgcm9sZUlkOiByb2xlSWQsXG4gICAgICAgIGFzc2lnbmVkQnk6IGN1cnJlbnRVc2VyLmlkLFxuICAgICAgfSkpO1xuXG4gICAgICBpZiAocmVwbGFjZV9leGlzdGluZykge1xuICAgICAgICBhd2FpdCBwcmlzbWEudXNlclJvbGUuY3JlYXRlTWFueSh7XG4gICAgICAgICAgZGF0YTogdXNlclJvbGVEYXRhLFxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIENoZWNrIGZvciBleGlzdGluZyBhc3NpZ25tZW50cyB0byBhdm9pZCBkdXBsaWNhdGVzXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nQXNzaWdubWVudHMgPSBhd2FpdCBwcmlzbWEudXNlclJvbGUuZmluZE1hbnkoe1xuICAgICAgICAgIHdoZXJlOiB7XG4gICAgICAgICAgICB1c2VySWQ6IGlkLFxuICAgICAgICAgICAgcm9sZUlkOiB7IGluOiByb2xlX2lkcyB9LFxuICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nUm9sZUlkcyA9IGV4aXN0aW5nQXNzaWdubWVudHMubWFwKHVhID0+IHVhLnJvbGVJZCk7XG4gICAgICAgIGNvbnN0IG5ld1JvbGVJZHMgPSByb2xlX2lkcy5maWx0ZXIoKHJvbGVJZDogc3RyaW5nKSA9PiAhZXhpc3RpbmdSb2xlSWRzLmluY2x1ZGVzKHJvbGVJZCkpO1xuXG4gICAgICAgIGlmIChuZXdSb2xlSWRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBjb25zdCBuZXdVc2VyUm9sZURhdGEgPSBuZXdSb2xlSWRzLm1hcCgocm9sZUlkOiBzdHJpbmcpID0+ICh7XG4gICAgICAgICAgICB1c2VySWQ6IGlkLFxuICAgICAgICAgICAgcm9sZUlkOiByb2xlSWQsXG4gICAgICAgICAgICBhc3NpZ25lZEJ5OiBjdXJyZW50VXNlci5pZCxcbiAgICAgICAgICB9KSk7XG5cbiAgICAgICAgICBhd2FpdCBwcmlzbWEudXNlclJvbGUuY3JlYXRlTWFueSh7XG4gICAgICAgICAgICBkYXRhOiBuZXdVc2VyUm9sZURhdGEsXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBGZXRjaCB1cGRhdGVkIHVzZXIgd2l0aCByb2xlc1xuICAgIGNvbnN0IHVzZXJXaXRoUm9sZXMgPSBhd2FpdCBwcmlzbWEudXNlci5maW5kVW5pcXVlKHtcbiAgICAgIHdoZXJlOiB7IGlkIH0sXG4gICAgICBpbmNsdWRlOiB7XG4gICAgICAgIHVzZXJSb2xlczoge1xuICAgICAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgICAgIHJvbGU6IHtcbiAgICAgICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgICAgIHJvbGVQZXJtaXNzaW9uczoge1xuICAgICAgICAgICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgICAgICAgICBwZXJtaXNzaW9uOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGNvbnN0IHRyYW5zZm9ybWVkUm9sZXMgPSB1c2VyV2l0aFJvbGVzIS51c2VyUm9sZXMubWFwKHVyID0+ICh7XG4gICAgICBpZDogdXIucm9sZS5pZCxcbiAgICAgIG5hbWU6IHVyLnJvbGUubmFtZSxcbiAgICAgIGRlc2NyaXB0aW9uOiB1ci5yb2xlLmRlc2NyaXB0aW9uLFxuICAgICAgYXNzaWduZWRfYXQ6IHVyLmFzc2lnbmVkQXQsXG4gICAgfSkpO1xuXG4gICAgcmV0dXJuIFJlc3BvbnNlLmpzb24oXG4gICAgICBjcmVhdGVBcGlSZXNwb25zZSh7XG4gICAgICAgIG1lc3NhZ2U6ICdVc2VyIHJvbGVzIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgICAgICAgdXNlcjoge1xuICAgICAgICAgIGlkOiB1c2VyV2l0aFJvbGVzIS5pZCxcbiAgICAgICAgICBlbWFpbDogdXNlcldpdGhSb2xlcyEuZW1haWwsXG4gICAgICAgICAgZnVsbF9uYW1lOiB1c2VyV2l0aFJvbGVzIS5mdWxsTmFtZSxcbiAgICAgICAgICByb2xlczogdHJhbnNmb3JtZWRSb2xlcyxcbiAgICAgICAgfSxcbiAgICAgICAgb3BlcmF0aW9uOiByZXBsYWNlX2V4aXN0aW5nID8gJ3JlcGxhY2VkJyA6ICdhZGRlZCcsXG4gICAgICAgIHJvbGVzX2Fzc2lnbmVkOiByb2xlX2lkcy5sZW5ndGgsXG4gICAgICB9KSxcbiAgICAgIHsgXG4gICAgICAgIHN0YXR1czogMjAwLFxuICAgICAgICBoZWFkZXJzOiBjb3JzSGVhZGVycygpLFxuICAgICAgfVxuICAgICk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIGhhbmRsZUVycm9yKGVycm9yLCAnRmFpbGVkIHRvIGFzc2lnbiB1c2VyIHJvbGVzJyk7XG4gIH1cbn1cblxuYXN5bmMgZnVuY3Rpb24gcmVtb3ZlVXNlclJvbGVIYW5kbGVyKHJlcXVlc3Q6IE5leHRSZXF1ZXN0LCBjb250ZXh0OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfSwgY3VycmVudFVzZXI6IGFueSkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgaWQgfSA9IGNvbnRleHQucGFyYW1zO1xuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCBnZXRSZXF1ZXN0Qm9keShyZXF1ZXN0KTtcbiAgICBcbiAgICBjb25zdCByZW1vdmVSb2xlU2NoZW1hID0gSm9pLm9iamVjdCh7XG4gICAgICByb2xlX2lkOiBKb2kuc3RyaW5nKCkudXVpZCgpLnJlcXVpcmVkKCksXG4gICAgfSk7XG5cbiAgICBjb25zdCB2YWxpZGF0aW9uID0gdmFsaWRhdGVSZXF1ZXN0KHJlbW92ZVJvbGVTY2hlbWEsIGJvZHkpO1xuICAgIGlmICghdmFsaWRhdGlvbi5pc1ZhbGlkKSB7XG4gICAgICByZXR1cm4gUmVzcG9uc2UuanNvbihcbiAgICAgICAgY3JlYXRlQXBpUmVzcG9uc2UobnVsbCwgJ1ZhbGlkYXRpb24gZmFpbGVkJywgJ1ZBTElEQVRJT05fRVJST1InKSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnN0IHsgcm9sZV9pZCB9ID0gdmFsaWRhdGlvbi5kYXRhO1xuXG4gICAgLy8gVmVyaWZ5IHVzZXIgZXhpc3RzXG4gICAgY29uc3QgdXNlciA9IGF3YWl0IHByaXNtYS51c2VyLmZpbmRVbmlxdWUoe1xuICAgICAgd2hlcmU6IHsgaWQgfSxcbiAgICB9KTtcblxuICAgIGlmICghdXNlcikge1xuICAgICAgcmV0dXJuIFJlc3BvbnNlLmpzb24oXG4gICAgICAgIGNyZWF0ZUFwaVJlc3BvbnNlKG51bGwsICdVc2VyIG5vdCBmb3VuZCcsICdOT1RfRk9VTkQnKSxcbiAgICAgICAgeyBzdGF0dXM6IDQwNCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHVzZXIgaGFzIHRoaXMgcm9sZVxuICAgIGNvbnN0IHVzZXJSb2xlID0gYXdhaXQgcHJpc21hLnVzZXJSb2xlLmZpbmRGaXJzdCh7XG4gICAgICB3aGVyZToge1xuICAgICAgICB1c2VySWQ6IGlkLFxuICAgICAgICByb2xlSWQ6IHJvbGVfaWQsXG4gICAgICB9LFxuICAgICAgaW5jbHVkZToge1xuICAgICAgICByb2xlOiB0cnVlLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmICghdXNlclJvbGUpIHtcbiAgICAgIHJldHVybiBSZXNwb25zZS5qc29uKFxuICAgICAgICBjcmVhdGVBcGlSZXNwb25zZShudWxsLCAnVXNlciBkb2VzIG5vdCBoYXZlIHRoaXMgcm9sZScsICdST0xFX05PVF9BU1NJR05FRCcpLFxuICAgICAgICB7IHN0YXR1czogNDA0IH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gUmVtb3ZlIHRoZSByb2xlXG4gICAgYXdhaXQgcHJpc21hLnVzZXJSb2xlLmRlbGV0ZSh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBpZDogdXNlclJvbGUuaWQsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgcmV0dXJuIFJlc3BvbnNlLmpzb24oXG4gICAgICBjcmVhdGVBcGlSZXNwb25zZSh7XG4gICAgICAgIG1lc3NhZ2U6ICdSb2xlIHJlbW92ZWQgZnJvbSB1c2VyIHN1Y2Nlc3NmdWxseScsXG4gICAgICAgIHJlbW92ZWRfcm9sZToge1xuICAgICAgICAgIGlkOiB1c2VyUm9sZS5yb2xlLmlkLFxuICAgICAgICAgIG5hbWU6IHVzZXJSb2xlLnJvbGUubmFtZSxcbiAgICAgICAgfSxcbiAgICAgIH0pLFxuICAgICAgeyBcbiAgICAgICAgc3RhdHVzOiAyMDAsXG4gICAgICAgIGhlYWRlcnM6IGNvcnNIZWFkZXJzKCksXG4gICAgICB9XG4gICAgKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICByZXR1cm4gaGFuZGxlRXJyb3IoZXJyb3IsICdGYWlsZWQgdG8gcmVtb3ZlIHVzZXIgcm9sZScpO1xuICB9XG59XG5cbmV4cG9ydCBjb25zdCBHRVQgPSByZXF1aXJlQXV0aChnZXRVc2VyUm9sZXNIYW5kbGVyKTtcbmV4cG9ydCBjb25zdCBQT1NUID0gcmVxdWlyZVJvbGUoWydhZG1pbiddKShhc3NpZ25Vc2VyUm9sZXNIYW5kbGVyKTtcbmV4cG9ydCBjb25zdCBERUxFVEUgPSByZXF1aXJlUm9sZShbJ2FkbWluJ10pKHJlbW92ZVVzZXJSb2xlSGFuZGxlcik7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBPUFRJT05TKCkge1xuICByZXR1cm4gbmV3IFJlc3BvbnNlKG51bGwsIHtcbiAgICBzdGF0dXM6IDIwMCxcbiAgICBoZWFkZXJzOiBjb3JzSGVhZGVycygpLFxuICB9KTtcbn1cbiJdLCJuYW1lcyI6WyJwcmlzbWEiLCJyZXF1aXJlQXV0aCIsInJlcXVpcmVSb2xlIiwiY3JlYXRlQXBpUmVzcG9uc2UiLCJnZXRSZXF1ZXN0Qm9keSIsImhhbmRsZUVycm9yIiwiY29yc0hlYWRlcnMiLCJ2YWxpZGF0ZVJlcXVlc3QiLCJKb2kiLCJhc3NpZ25Sb2xlc1NjaGVtYSIsIm9iamVjdCIsInJvbGVfaWRzIiwiYXJyYXkiLCJpdGVtcyIsInN0cmluZyIsInV1aWQiLCJtaW4iLCJyZXF1aXJlZCIsInJlcGxhY2VfZXhpc3RpbmciLCJib29sZWFuIiwiZGVmYXVsdCIsImdldFVzZXJSb2xlc0hhbmRsZXIiLCJyZXF1ZXN0IiwiY29udGV4dCIsImN1cnJlbnRVc2VyIiwiaWQiLCJwYXJhbXMiLCJ1c2VyIiwiZmluZFVuaXF1ZSIsIndoZXJlIiwic2VsZWN0IiwiZW1haWwiLCJmdWxsTmFtZSIsImlzQWN0aXZlIiwiUmVzcG9uc2UiLCJqc29uIiwic3RhdHVzIiwidXNlclJvbGVzIiwidXNlclJvbGUiLCJmaW5kTWFueSIsInVzZXJJZCIsImluY2x1ZGUiLCJyb2xlIiwicm9sZVBlcm1pc3Npb25zIiwicGVybWlzc2lvbiIsIm9yZGVyQnkiLCJhc3NpZ25lZEF0IiwidHJhbnNmb3JtZWRSb2xlcyIsIm1hcCIsInVyIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiaXNfc3lzdGVtX3JvbGUiLCJpc1N5c3RlbVJvbGUiLCJhc3NpZ25lZF9hdCIsImFzc2lnbmVkX2J5IiwiYXNzaWduZWRCeSIsInBlcm1pc3Npb25zIiwicnAiLCJyZXNvdXJjZSIsImFjdGlvbiIsImFsbFBlcm1pc3Npb25zIiwiZmxhdE1hcCIsImZyb21fcm9sZSIsInVuaXF1ZVBlcm1pc3Npb25zIiwiZmlsdGVyIiwiaW5kZXgiLCJzZWxmIiwiZmluZEluZGV4IiwicCIsImZ1bGxfbmFtZSIsImlzX2FjdGl2ZSIsInJvbGVzIiwicm9sZV9jb3VudCIsImxlbmd0aCIsInBlcm1pc3Npb25fY291bnQiLCJoZWFkZXJzIiwiZXJyb3IiLCJhc3NpZ25Vc2VyUm9sZXNIYW5kbGVyIiwiYm9keSIsInZhbGlkYXRpb24iLCJpc1ZhbGlkIiwiZGF0YSIsImV4aXN0aW5nUm9sZXMiLCJpbiIsIm1pc3NpbmdSb2xlcyIsInJvbGVJZCIsImZpbmQiLCJyIiwiam9pbiIsImRlbGV0ZU1hbnkiLCJ1c2VyUm9sZURhdGEiLCJjcmVhdGVNYW55IiwiZXhpc3RpbmdBc3NpZ25tZW50cyIsImV4aXN0aW5nUm9sZUlkcyIsInVhIiwibmV3Um9sZUlkcyIsImluY2x1ZGVzIiwibmV3VXNlclJvbGVEYXRhIiwidXNlcldpdGhSb2xlcyIsIm1lc3NhZ2UiLCJvcGVyYXRpb24iLCJyb2xlc19hc3NpZ25lZCIsInJlbW92ZVVzZXJSb2xlSGFuZGxlciIsInJlbW92ZVJvbGVTY2hlbWEiLCJyb2xlX2lkIiwiZmluZEZpcnN0IiwiZGVsZXRlIiwicmVtb3ZlZF9yb2xlIiwiR0VUIiwiUE9TVCIsIkRFTEVURSIsIk9QVElPTlMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/users/[id]/roles/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comparePassword: () => (/* binding */ comparePassword),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   getUserRoles: () => (/* binding */ getUserRoles),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"fallback-secret\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"7d\";\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function comparePassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nasync function getUserRoles(userId) {\n    const userRoles = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.userRole.findMany({\n        where: {\n            userId\n        },\n        include: {\n            role: true\n        }\n    });\n    return userRoles.map((ur)=>ur.role.name);\n}\nasync function getAuthUser(request) {\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return null;\n        }\n        const token = authHeader.substring(7);\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            select: {\n                id: true,\n                email: true,\n                fullName: true,\n                phone: true,\n                isActive: true,\n                createdAt: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        // Get user roles\n        const roles = await getUserRoles(user.id);\n        return {\n            ...user,\n            roles\n        };\n    } catch (error) {\n        return null;\n    }\n}\nfunction requireAuth(handler) {\n    return async (request, context)=>{\n        const user = await getAuthUser(request);\n        if (!user) {\n            return Response.json({\n                success: false,\n                error: \"Unauthorized\",\n                code: \"UNAUTHORIZED\"\n            }, {\n                status: 401\n            });\n        }\n        return handler(request, context, user);\n    };\n}\nfunction requireRole(roles) {\n    return (handler)=>{\n        return async (request, context)=>{\n            const user = await getAuthUser(request);\n            if (!user) {\n                return Response.json({\n                    success: false,\n                    error: \"Unauthorized\",\n                    code: \"UNAUTHORIZED\"\n                }, {\n                    status: 401\n                });\n            }\n            const hasRole = roles.some((role)=>user.roles.includes(role));\n            if (!hasRole) {\n                return Response.json({\n                    success: false,\n                    error: \"Insufficient permissions\",\n                    code: \"FORBIDDEN\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(request, context, user);\n        };\n    };\n}\nasync function hasPermission(userId, resource, action) {\n    const userRoles = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.userRole.findMany({\n        where: {\n            userId\n        },\n        include: {\n            role: {\n                include: {\n                    rolePermissions: {\n                        include: {\n                            permission: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n    for (const userRole of userRoles){\n        for (const rolePermission of userRole.role.rolePermissions){\n            const permission = rolePermission.permission;\n            if (permission.resource === resource && permission.action === action) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\nfunction requirePermission(resource, action) {\n    return (handler)=>{\n        return async (request, context)=>{\n            const user = await getAuthUser(request);\n            if (!user) {\n                return Response.json({\n                    success: false,\n                    error: \"Unauthorized\",\n                    code: \"UNAUTHORIZED\"\n                }, {\n                    status: 401\n                });\n            }\n            const hasAccess = await hasPermission(user.id, resource, action);\n            if (!hasAccess) {\n                return Response.json({\n                    success: false,\n                    error: \"Insufficient permissions\",\n                    code: \"FORBIDDEN\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(request, context, user);\n        };\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUU5QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFHO0FBRW5FLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Nyc3ItcHJvcGVydHktbWFuYWdlbWVudC1iYWNrZW5kLy4vbGliL3ByaXNtYS50cz85ODIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   corsHeaders: () => (/* binding */ corsHeaders),\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   createPaginationResponse: () => (/* binding */ createPaginationResponse),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   getQueryParams: () => (/* binding */ getQueryParams),\n/* harmony export */   getRequestBody: () => (/* binding */ getRequestBody),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   parseDate: () => (/* binding */ parseDate)\n/* harmony export */ });\nfunction createApiResponse(data, error, code) {\n    return {\n        success: !error,\n        ...data && {\n            data\n        },\n        ...error && {\n            error\n        },\n        ...code && {\n            code\n        }\n    };\n}\nfunction createPaginationResponse(data, page, limit, total) {\n    const pages = Math.ceil(total / limit);\n    return {\n        success: true,\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            pages,\n            has_next: page < pages,\n            has_prev: page > 1\n        }\n    };\n}\nfunction getQueryParams(request) {\n    const { searchParams } = new URL(request.url);\n    const params = {};\n    searchParams.forEach((value, key)=>{\n        // Handle numeric values\n        if (!isNaN(Number(value))) {\n            params[key] = Number(value);\n        } else if (value === \"true\" || value === \"false\") {\n            // Handle boolean values\n            params[key] = value === \"true\";\n        } else {\n            params[key] = value;\n        }\n    });\n    return params;\n}\nasync function getRequestBody(request) {\n    try {\n        return await request.json();\n    } catch (error) {\n        return null;\n    }\n}\nfunction handleError(error, defaultMessage = \"Internal server error\", context) {\n    console.error(`API Error${context ? ` (${context})` : \"\"}:`, error);\n    // Rate limiting errors (from V1 patterns)\n    if (isRateLimitError(error)) {\n        return Response.json(createApiResponse(null, \"Rate limit exceeded. Please try again in a moment.\", \"RATE_LIMIT_EXCEEDED\"), {\n            status: 429,\n            headers: {\n                ...corsHeaders(),\n                \"Retry-After\": \"60\"\n            }\n        });\n    }\n    // JSON parsing errors (often related to rate limiting)\n    if (isJsonParsingError(error)) {\n        return Response.json(createApiResponse(null, \"Request parsing failed. Please try again.\", \"PARSING_ERROR\"), {\n            status: 400,\n            headers: corsHeaders()\n        });\n    }\n    // Database constraint errors\n    if (error.code === \"P2002\") {\n        const field = error.meta?.target?.[0] || \"field\";\n        return Response.json(createApiResponse(null, `${field} already exists`, \"DUPLICATE_ENTRY\"), {\n            status: 409,\n            headers: corsHeaders()\n        });\n    }\n    if (error.code === \"P2025\") {\n        return Response.json(createApiResponse(null, \"Resource not found\", \"NOT_FOUND\"), {\n            status: 404,\n            headers: corsHeaders()\n        });\n    }\n    // Database connection errors\n    if (error.code === \"P1001\" || error.code === \"P1008\") {\n        return Response.json(createApiResponse(null, \"Database connection failed. Please try again.\", \"DATABASE_ERROR\"), {\n            status: 503,\n            headers: corsHeaders()\n        });\n    }\n    // Validation errors\n    if (error.name === \"ValidationError\" || error.isJoi) {\n        return Response.json(createApiResponse(null, error.message || \"Validation failed\", \"VALIDATION_ERROR\"), {\n            status: 400,\n            headers: corsHeaders()\n        });\n    }\n    // Network/timeout errors\n    if (error.code === \"ECONNRESET\" || error.code === \"ETIMEDOUT\") {\n        return Response.json(createApiResponse(null, \"Network error. Please try again.\", \"NETWORK_ERROR\"), {\n            status: 503,\n            headers: corsHeaders()\n        });\n    }\n    return Response.json(createApiResponse(null, defaultMessage, \"INTERNAL_ERROR\"), {\n        status: 500,\n        headers: corsHeaders()\n    });\n}\n// Helper functions for error detection (from V1 patterns)\nfunction isRateLimitError(error) {\n    const message = (error?.message || \"\").toLowerCase();\n    return message.includes(\"too many requests\") || message.includes(\"rate limit\") || message.includes(\"429\") || error?.response?.status === 429;\n}\nfunction isJsonParsingError(error) {\n    const message = (error?.message || \"\").toLowerCase();\n    return error instanceof SyntaxError || message.includes(\"unexpected token\") || message.includes(\"json\") || message.includes(\"syntaxerror\");\n}\nfunction corsHeaders() {\n    return {\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n        \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n    };\n}\n// Date utilities\nfunction formatDate(date) {\n    return date.toISOString().split(\"T\")[0];\n}\nfunction formatDateTime(date) {\n    return date.toISOString();\n}\nfunction parseDate(dateString) {\n    return new Date(dateString);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/validation.ts":
/*!***************************!*\
  !*** ./lib/validation.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createFuelLogSchema: () => (/* binding */ createFuelLogSchema),\n/* harmony export */   createMaintenanceIssueSchema: () => (/* binding */ createMaintenanceIssueSchema),\n/* harmony export */   createPropertySchema: () => (/* binding */ createPropertySchema),\n/* harmony export */   legacyLoginSchema: () => (/* binding */ legacyLoginSchema),\n/* harmony export */   loginSchema: () => (/* binding */ loginSchema),\n/* harmony export */   paginationSchema: () => (/* binding */ paginationSchema),\n/* harmony export */   registerSchema: () => (/* binding */ registerSchema),\n/* harmony export */   submitAttendanceSchema: () => (/* binding */ submitAttendanceSchema),\n/* harmony export */   validateRequest: () => (/* binding */ validateRequest)\n/* harmony export */ });\n/* harmony import */ var joi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! joi */ \"(rsc)/./node_modules/joi/lib/index.js\");\n/* harmony import */ var joi__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(joi__WEBPACK_IMPORTED_MODULE_0__);\n\n// Auth validation schemas\nconst loginSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    identifier: joi__WEBPACK_IMPORTED_MODULE_0___default().string().required(),\n    password: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(6).required(),\n    login_type: joi__WEBPACK_IMPORTED_MODULE_0___default().string().valid(\"email\", \"username\", \"phone\").optional()\n});\nconst legacyLoginSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    email: joi__WEBPACK_IMPORTED_MODULE_0___default().string().email().required(),\n    password: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(6).required()\n});\nconst registerSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    email: joi__WEBPACK_IMPORTED_MODULE_0___default().string().email().required(),\n    username: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(3).max(50).pattern(/^[a-zA-Z0-9_-]+$/).optional(),\n    password: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(6).required(),\n    full_name: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(2).required(),\n    phone: joi__WEBPACK_IMPORTED_MODULE_0___default().string().pattern(/^[+]?[1-9]\\d{1,14}$/).optional()\n});\n// Property validation schemas\nconst createPropertySchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    name: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(2).required(),\n    type: joi__WEBPACK_IMPORTED_MODULE_0___default().string().valid(\"residential\", \"office\", \"construction_site\").required(),\n    parent_property_id: joi__WEBPACK_IMPORTED_MODULE_0___default().string().uuid().optional(),\n    address: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n    description: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n    // Office-specific fields\n    capacity: joi__WEBPACK_IMPORTED_MODULE_0___default().number().integer().min(1).when(\"type\", {\n        is: \"office\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().optional(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    department: joi__WEBPACK_IMPORTED_MODULE_0___default().string().when(\"type\", {\n        is: \"office\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().optional(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    // Site-specific fields\n    project_type: joi__WEBPACK_IMPORTED_MODULE_0___default().string().when(\"type\", {\n        is: \"construction_site\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().required(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    start_date: joi__WEBPACK_IMPORTED_MODULE_0___default().date().when(\"type\", {\n        is: \"construction_site\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().required(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    expected_end_date: joi__WEBPACK_IMPORTED_MODULE_0___default().date().when(\"type\", {\n        is: \"construction_site\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().required(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    hourly_rate_standard: joi__WEBPACK_IMPORTED_MODULE_0___default().number().positive().when(\"type\", {\n        is: \"construction_site\",\n        then: joi__WEBPACK_IMPORTED_MODULE_0___default().optional(),\n        otherwise: joi__WEBPACK_IMPORTED_MODULE_0___default().forbidden()\n    }),\n    // Common fields\n    location: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional()\n});\n// Maintenance validation schemas\nconst createMaintenanceIssueSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    property_id: joi__WEBPACK_IMPORTED_MODULE_0___default().string().uuid().required(),\n    title: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(2).required(),\n    description: joi__WEBPACK_IMPORTED_MODULE_0___default().string().min(5).required(),\n    priority: joi__WEBPACK_IMPORTED_MODULE_0___default().string().valid(\"low\", \"medium\", \"high\", \"critical\").required(),\n    service_type: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n    department: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n    due_date: joi__WEBPACK_IMPORTED_MODULE_0___default().date().optional()\n});\n// Attendance validation schemas\nconst submitAttendanceSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    date: joi__WEBPACK_IMPORTED_MODULE_0___default().date().required(),\n    attendance: joi__WEBPACK_IMPORTED_MODULE_0___default().array().items(joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n        worker_id: joi__WEBPACK_IMPORTED_MODULE_0___default().string().uuid().required(),\n        status: joi__WEBPACK_IMPORTED_MODULE_0___default().string().valid(\"present\", \"absent\", \"late\", \"half_day\").required(),\n        check_in_time: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n        check_out_time: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional(),\n        hours_worked: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).max(24).optional(),\n        notes: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional()\n    })).required()\n});\n// Generator fuel validation schemas\nconst createFuelLogSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    fuel_level_liters: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).required(),\n    consumption_rate: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).optional(),\n    runtime_hours: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).optional(),\n    efficiency_percentage: joi__WEBPACK_IMPORTED_MODULE_0___default().number().min(0).max(100).optional(),\n    notes: joi__WEBPACK_IMPORTED_MODULE_0___default().string().optional()\n});\n// Pagination validation\nconst paginationSchema = joi__WEBPACK_IMPORTED_MODULE_0___default().object({\n    page: joi__WEBPACK_IMPORTED_MODULE_0___default().number().integer().min(1).default(1),\n    limit: joi__WEBPACK_IMPORTED_MODULE_0___default().number().integer().min(1).max(100).default(10)\n});\n// Validation helper function\nfunction validateRequest(schema, data) {\n    const { error, value } = schema.validate(data, {\n        abortEarly: false\n    });\n    if (error) {\n        const errors = error.details.map((detail)=>({\n                field: detail.path.join(\".\"),\n                message: detail.message\n            }));\n        return {\n            isValid: false,\n            errors,\n            data: null\n        };\n    }\n    return {\n        isValid: true,\n        errors: null,\n        data: value\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdmFsaWRhdGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFzQjtBQUV0QiwwQkFBMEI7QUFDbkIsTUFBTUMsY0FBY0QsaURBQVUsQ0FBQztJQUNwQ0csWUFBWUgsaURBQVUsR0FBR0ssUUFBUTtJQUNqQ0MsVUFBVU4saURBQVUsR0FBR08sR0FBRyxDQUFDLEdBQUdGLFFBQVE7SUFDdENHLFlBQVlSLGlEQUFVLEdBQUdTLEtBQUssQ0FBQyxTQUFTLFlBQVksU0FBU0MsUUFBUTtBQUN2RSxHQUFHO0FBRUksTUFBTUMsb0JBQW9CWCxpREFBVSxDQUFDO0lBQzFDWSxPQUFPWixpREFBVSxHQUFHWSxLQUFLLEdBQUdQLFFBQVE7SUFDcENDLFVBQVVOLGlEQUFVLEdBQUdPLEdBQUcsQ0FBQyxHQUFHRixRQUFRO0FBQ3hDLEdBQUc7QUFFSSxNQUFNUSxpQkFBaUJiLGlEQUFVLENBQUM7SUFDdkNZLE9BQU9aLGlEQUFVLEdBQUdZLEtBQUssR0FBR1AsUUFBUTtJQUNwQ1MsVUFBVWQsaURBQVUsR0FBR08sR0FBRyxDQUFDLEdBQUdRLEdBQUcsQ0FBQyxJQUFJQyxPQUFPLENBQUMsb0JBQW9CTixRQUFRO0lBQzFFSixVQUFVTixpREFBVSxHQUFHTyxHQUFHLENBQUMsR0FBR0YsUUFBUTtJQUN0Q1ksV0FBV2pCLGlEQUFVLEdBQUdPLEdBQUcsQ0FBQyxHQUFHRixRQUFRO0lBQ3ZDYSxPQUFPbEIsaURBQVUsR0FBR2dCLE9BQU8sQ0FBQyx1QkFBdUJOLFFBQVE7QUFDN0QsR0FBRztBQUVILDhCQUE4QjtBQUN2QixNQUFNUyx1QkFBdUJuQixpREFBVSxDQUFDO0lBQzdDb0IsTUFBTXBCLGlEQUFVLEdBQUdPLEdBQUcsQ0FBQyxHQUFHRixRQUFRO0lBQ2xDZ0IsTUFBTXJCLGlEQUFVLEdBQUdTLEtBQUssQ0FBQyxlQUFlLFVBQVUscUJBQXFCSixRQUFRO0lBQy9FaUIsb0JBQW9CdEIsaURBQVUsR0FBR3VCLElBQUksR0FBR2IsUUFBUTtJQUNoRGMsU0FBU3hCLGlEQUFVLEdBQUdVLFFBQVE7SUFDOUJlLGFBQWF6QixpREFBVSxHQUFHVSxRQUFRO0lBRWxDLHlCQUF5QjtJQUN6QmdCLFVBQVUxQixpREFBVSxHQUFHNEIsT0FBTyxHQUFHckIsR0FBRyxDQUFDLEdBQUdzQixJQUFJLENBQUMsUUFBUTtRQUFFQyxJQUFJO1FBQVVDLE1BQU0vQixtREFBWTtRQUFJZ0MsV0FBV2hDLG9EQUFhO0lBQUc7SUFDdEhrQyxZQUFZbEMsaURBQVUsR0FBRzZCLElBQUksQ0FBQyxRQUFRO1FBQUVDLElBQUk7UUFBVUMsTUFBTS9CLG1EQUFZO1FBQUlnQyxXQUFXaEMsb0RBQWE7SUFBRztJQUV2Ryx1QkFBdUI7SUFDdkJtQyxjQUFjbkMsaURBQVUsR0FBRzZCLElBQUksQ0FBQyxRQUFRO1FBQUVDLElBQUk7UUFBcUJDLE1BQU0vQixtREFBWTtRQUFJZ0MsV0FBV2hDLG9EQUFhO0lBQUc7SUFDcEhvQyxZQUFZcEMsK0NBQVEsR0FBRzZCLElBQUksQ0FBQyxRQUFRO1FBQUVDLElBQUk7UUFBcUJDLE1BQU0vQixtREFBWTtRQUFJZ0MsV0FBV2hDLG9EQUFhO0lBQUc7SUFDaEhzQyxtQkFBbUJ0QywrQ0FBUSxHQUFHNkIsSUFBSSxDQUFDLFFBQVE7UUFBRUMsSUFBSTtRQUFxQkMsTUFBTS9CLG1EQUFZO1FBQUlnQyxXQUFXaEMsb0RBQWE7SUFBRztJQUN2SHVDLHNCQUFzQnZDLGlEQUFVLEdBQUd3QyxRQUFRLEdBQUdYLElBQUksQ0FBQyxRQUFRO1FBQUVDLElBQUk7UUFBcUJDLE1BQU0vQixtREFBWTtRQUFJZ0MsV0FBV2hDLG9EQUFhO0lBQUc7SUFFdkksZ0JBQWdCO0lBQ2hCeUMsVUFBVXpDLGlEQUFVLEdBQUdVLFFBQVE7QUFDakMsR0FBRztBQUVILGlDQUFpQztBQUMxQixNQUFNZ0MsK0JBQStCMUMsaURBQVUsQ0FBQztJQUNyRDJDLGFBQWEzQyxpREFBVSxHQUFHdUIsSUFBSSxHQUFHbEIsUUFBUTtJQUN6Q3VDLE9BQU81QyxpREFBVSxHQUFHTyxHQUFHLENBQUMsR0FBR0YsUUFBUTtJQUNuQ29CLGFBQWF6QixpREFBVSxHQUFHTyxHQUFHLENBQUMsR0FBR0YsUUFBUTtJQUN6Q3dDLFVBQVU3QyxpREFBVSxHQUFHUyxLQUFLLENBQUMsT0FBTyxVQUFVLFFBQVEsWUFBWUosUUFBUTtJQUMxRXlDLGNBQWM5QyxpREFBVSxHQUFHVSxRQUFRO0lBQ25Dd0IsWUFBWWxDLGlEQUFVLEdBQUdVLFFBQVE7SUFDakNxQyxVQUFVL0MsK0NBQVEsR0FBR1UsUUFBUTtBQUMvQixHQUFHO0FBRUgsZ0NBQWdDO0FBQ3pCLE1BQU1zQyx5QkFBeUJoRCxpREFBVSxDQUFDO0lBQy9DcUMsTUFBTXJDLCtDQUFRLEdBQUdLLFFBQVE7SUFDekI0QyxZQUFZakQsZ0RBQVMsR0FBR21ELEtBQUssQ0FDM0JuRCxpREFBVSxDQUFDO1FBQ1RvRCxXQUFXcEQsaURBQVUsR0FBR3VCLElBQUksR0FBR2xCLFFBQVE7UUFDdkNnRCxRQUFRckQsaURBQVUsR0FBR1MsS0FBSyxDQUFDLFdBQVcsVUFBVSxRQUFRLFlBQVlKLFFBQVE7UUFDNUVpRCxlQUFldEQsaURBQVUsR0FBR1UsUUFBUTtRQUNwQzZDLGdCQUFnQnZELGlEQUFVLEdBQUdVLFFBQVE7UUFDckM4QyxjQUFjeEQsaURBQVUsR0FBR08sR0FBRyxDQUFDLEdBQUdRLEdBQUcsQ0FBQyxJQUFJTCxRQUFRO1FBQ2xEK0MsT0FBT3pELGlEQUFVLEdBQUdVLFFBQVE7SUFDOUIsSUFDQUwsUUFBUTtBQUNaLEdBQUc7QUFFSCxvQ0FBb0M7QUFDN0IsTUFBTXFELHNCQUFzQjFELGlEQUFVLENBQUM7SUFDNUMyRCxtQkFBbUIzRCxpREFBVSxHQUFHTyxHQUFHLENBQUMsR0FBR0YsUUFBUTtJQUMvQ3VELGtCQUFrQjVELGlEQUFVLEdBQUdPLEdBQUcsQ0FBQyxHQUFHRyxRQUFRO0lBQzlDbUQsZUFBZTdELGlEQUFVLEdBQUdPLEdBQUcsQ0FBQyxHQUFHRyxRQUFRO0lBQzNDb0QsdUJBQXVCOUQsaURBQVUsR0FBR08sR0FBRyxDQUFDLEdBQUdRLEdBQUcsQ0FBQyxLQUFLTCxRQUFRO0lBQzVEK0MsT0FBT3pELGlEQUFVLEdBQUdVLFFBQVE7QUFDOUIsR0FBRztBQUVILHdCQUF3QjtBQUNqQixNQUFNcUQsbUJBQW1CL0QsaURBQVUsQ0FBQztJQUN6Q2dFLE1BQU1oRSxpREFBVSxHQUFHNEIsT0FBTyxHQUFHckIsR0FBRyxDQUFDLEdBQUcwRCxPQUFPLENBQUM7SUFDNUNDLE9BQU9sRSxpREFBVSxHQUFHNEIsT0FBTyxHQUFHckIsR0FBRyxDQUFDLEdBQUdRLEdBQUcsQ0FBQyxLQUFLa0QsT0FBTyxDQUFDO0FBQ3hELEdBQUc7QUFFSCw2QkFBNkI7QUFDdEIsU0FBU0UsZ0JBQWdCQyxNQUF3QixFQUFFQyxJQUFTO0lBQ2pFLE1BQU0sRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQUUsR0FBR0gsT0FBT0ksUUFBUSxDQUFDSCxNQUFNO1FBQUVJLFlBQVk7SUFBTTtJQUVuRSxJQUFJSCxPQUFPO1FBQ1QsTUFBTUksU0FBU0osTUFBTUssT0FBTyxDQUFDQyxHQUFHLENBQUNDLENBQUFBLFNBQVc7Z0JBQzFDQyxPQUFPRCxPQUFPRSxJQUFJLENBQUNDLElBQUksQ0FBQztnQkFDeEJDLFNBQVNKLE9BQU9JLE9BQU87WUFDekI7UUFFQSxPQUFPO1lBQ0xDLFNBQVM7WUFDVFI7WUFDQUwsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxPQUFPO1FBQ0xhLFNBQVM7UUFDVFIsUUFBUTtRQUNSTCxNQUFNRTtJQUNSO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcnNyLXByb3BlcnR5LW1hbmFnZW1lbnQtYmFja2VuZC8uL2xpYi92YWxpZGF0aW9uLnRzPzUwYWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEpvaSBmcm9tICdqb2knO1xuXG4vLyBBdXRoIHZhbGlkYXRpb24gc2NoZW1hc1xuZXhwb3J0IGNvbnN0IGxvZ2luU2NoZW1hID0gSm9pLm9iamVjdCh7XG4gIGlkZW50aWZpZXI6IEpvaS5zdHJpbmcoKS5yZXF1aXJlZCgpLFxuICBwYXNzd29yZDogSm9pLnN0cmluZygpLm1pbig2KS5yZXF1aXJlZCgpLFxuICBsb2dpbl90eXBlOiBKb2kuc3RyaW5nKCkudmFsaWQoJ2VtYWlsJywgJ3VzZXJuYW1lJywgJ3Bob25lJykub3B0aW9uYWwoKSxcbn0pO1xuXG5leHBvcnQgY29uc3QgbGVnYWN5TG9naW5TY2hlbWEgPSBKb2kub2JqZWN0KHtcbiAgZW1haWw6IEpvaS5zdHJpbmcoKS5lbWFpbCgpLnJlcXVpcmVkKCksXG4gIHBhc3N3b3JkOiBKb2kuc3RyaW5nKCkubWluKDYpLnJlcXVpcmVkKCksXG59KTtcblxuZXhwb3J0IGNvbnN0IHJlZ2lzdGVyU2NoZW1hID0gSm9pLm9iamVjdCh7XG4gIGVtYWlsOiBKb2kuc3RyaW5nKCkuZW1haWwoKS5yZXF1aXJlZCgpLFxuICB1c2VybmFtZTogSm9pLnN0cmluZygpLm1pbigzKS5tYXgoNTApLnBhdHRlcm4oL15bYS16QS1aMC05Xy1dKyQvKS5vcHRpb25hbCgpLFxuICBwYXNzd29yZDogSm9pLnN0cmluZygpLm1pbig2KS5yZXF1aXJlZCgpLFxuICBmdWxsX25hbWU6IEpvaS5zdHJpbmcoKS5taW4oMikucmVxdWlyZWQoKSxcbiAgcGhvbmU6IEpvaS5zdHJpbmcoKS5wYXR0ZXJuKC9eWytdP1sxLTldXFxkezEsMTR9JC8pLm9wdGlvbmFsKCksXG59KTtcblxuLy8gUHJvcGVydHkgdmFsaWRhdGlvbiBzY2hlbWFzXG5leHBvcnQgY29uc3QgY3JlYXRlUHJvcGVydHlTY2hlbWEgPSBKb2kub2JqZWN0KHtcbiAgbmFtZTogSm9pLnN0cmluZygpLm1pbigyKS5yZXF1aXJlZCgpLFxuICB0eXBlOiBKb2kuc3RyaW5nKCkudmFsaWQoJ3Jlc2lkZW50aWFsJywgJ29mZmljZScsICdjb25zdHJ1Y3Rpb25fc2l0ZScpLnJlcXVpcmVkKCksXG4gIHBhcmVudF9wcm9wZXJ0eV9pZDogSm9pLnN0cmluZygpLnV1aWQoKS5vcHRpb25hbCgpLFxuICBhZGRyZXNzOiBKb2kuc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgZGVzY3JpcHRpb246IEpvaS5zdHJpbmcoKS5vcHRpb25hbCgpLFxuXG4gIC8vIE9mZmljZS1zcGVjaWZpYyBmaWVsZHNcbiAgY2FwYWNpdHk6IEpvaS5udW1iZXIoKS5pbnRlZ2VyKCkubWluKDEpLndoZW4oJ3R5cGUnLCB7IGlzOiAnb2ZmaWNlJywgdGhlbjogSm9pLm9wdGlvbmFsKCksIG90aGVyd2lzZTogSm9pLmZvcmJpZGRlbigpIH0pLFxuICBkZXBhcnRtZW50OiBKb2kuc3RyaW5nKCkud2hlbigndHlwZScsIHsgaXM6ICdvZmZpY2UnLCB0aGVuOiBKb2kub3B0aW9uYWwoKSwgb3RoZXJ3aXNlOiBKb2kuZm9yYmlkZGVuKCkgfSksXG5cbiAgLy8gU2l0ZS1zcGVjaWZpYyBmaWVsZHNcbiAgcHJvamVjdF90eXBlOiBKb2kuc3RyaW5nKCkud2hlbigndHlwZScsIHsgaXM6ICdjb25zdHJ1Y3Rpb25fc2l0ZScsIHRoZW46IEpvaS5yZXF1aXJlZCgpLCBvdGhlcndpc2U6IEpvaS5mb3JiaWRkZW4oKSB9KSxcbiAgc3RhcnRfZGF0ZTogSm9pLmRhdGUoKS53aGVuKCd0eXBlJywgeyBpczogJ2NvbnN0cnVjdGlvbl9zaXRlJywgdGhlbjogSm9pLnJlcXVpcmVkKCksIG90aGVyd2lzZTogSm9pLmZvcmJpZGRlbigpIH0pLFxuICBleHBlY3RlZF9lbmRfZGF0ZTogSm9pLmRhdGUoKS53aGVuKCd0eXBlJywgeyBpczogJ2NvbnN0cnVjdGlvbl9zaXRlJywgdGhlbjogSm9pLnJlcXVpcmVkKCksIG90aGVyd2lzZTogSm9pLmZvcmJpZGRlbigpIH0pLFxuICBob3VybHlfcmF0ZV9zdGFuZGFyZDogSm9pLm51bWJlcigpLnBvc2l0aXZlKCkud2hlbigndHlwZScsIHsgaXM6ICdjb25zdHJ1Y3Rpb25fc2l0ZScsIHRoZW46IEpvaS5vcHRpb25hbCgpLCBvdGhlcndpc2U6IEpvaS5mb3JiaWRkZW4oKSB9KSxcblxuICAvLyBDb21tb24gZmllbGRzXG4gIGxvY2F0aW9uOiBKb2kuc3RyaW5nKCkub3B0aW9uYWwoKSxcbn0pO1xuXG4vLyBNYWludGVuYW5jZSB2YWxpZGF0aW9uIHNjaGVtYXNcbmV4cG9ydCBjb25zdCBjcmVhdGVNYWludGVuYW5jZUlzc3VlU2NoZW1hID0gSm9pLm9iamVjdCh7XG4gIHByb3BlcnR5X2lkOiBKb2kuc3RyaW5nKCkudXVpZCgpLnJlcXVpcmVkKCksXG4gIHRpdGxlOiBKb2kuc3RyaW5nKCkubWluKDIpLnJlcXVpcmVkKCksXG4gIGRlc2NyaXB0aW9uOiBKb2kuc3RyaW5nKCkubWluKDUpLnJlcXVpcmVkKCksXG4gIHByaW9yaXR5OiBKb2kuc3RyaW5nKCkudmFsaWQoJ2xvdycsICdtZWRpdW0nLCAnaGlnaCcsICdjcml0aWNhbCcpLnJlcXVpcmVkKCksXG4gIHNlcnZpY2VfdHlwZTogSm9pLnN0cmluZygpLm9wdGlvbmFsKCksXG4gIGRlcGFydG1lbnQ6IEpvaS5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBkdWVfZGF0ZTogSm9pLmRhdGUoKS5vcHRpb25hbCgpLFxufSk7XG5cbi8vIEF0dGVuZGFuY2UgdmFsaWRhdGlvbiBzY2hlbWFzXG5leHBvcnQgY29uc3Qgc3VibWl0QXR0ZW5kYW5jZVNjaGVtYSA9IEpvaS5vYmplY3Qoe1xuICBkYXRlOiBKb2kuZGF0ZSgpLnJlcXVpcmVkKCksXG4gIGF0dGVuZGFuY2U6IEpvaS5hcnJheSgpLml0ZW1zKFxuICAgIEpvaS5vYmplY3Qoe1xuICAgICAgd29ya2VyX2lkOiBKb2kuc3RyaW5nKCkudXVpZCgpLnJlcXVpcmVkKCksXG4gICAgICBzdGF0dXM6IEpvaS5zdHJpbmcoKS52YWxpZCgncHJlc2VudCcsICdhYnNlbnQnLCAnbGF0ZScsICdoYWxmX2RheScpLnJlcXVpcmVkKCksXG4gICAgICBjaGVja19pbl90aW1lOiBKb2kuc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgICAgIGNoZWNrX291dF90aW1lOiBKb2kuc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgICAgIGhvdXJzX3dvcmtlZDogSm9pLm51bWJlcigpLm1pbigwKS5tYXgoMjQpLm9wdGlvbmFsKCksXG4gICAgICBub3RlczogSm9pLnN0cmluZygpLm9wdGlvbmFsKCksXG4gICAgfSlcbiAgKS5yZXF1aXJlZCgpLFxufSk7XG5cbi8vIEdlbmVyYXRvciBmdWVsIHZhbGlkYXRpb24gc2NoZW1hc1xuZXhwb3J0IGNvbnN0IGNyZWF0ZUZ1ZWxMb2dTY2hlbWEgPSBKb2kub2JqZWN0KHtcbiAgZnVlbF9sZXZlbF9saXRlcnM6IEpvaS5udW1iZXIoKS5taW4oMCkucmVxdWlyZWQoKSxcbiAgY29uc3VtcHRpb25fcmF0ZTogSm9pLm51bWJlcigpLm1pbigwKS5vcHRpb25hbCgpLFxuICBydW50aW1lX2hvdXJzOiBKb2kubnVtYmVyKCkubWluKDApLm9wdGlvbmFsKCksXG4gIGVmZmljaWVuY3lfcGVyY2VudGFnZTogSm9pLm51bWJlcigpLm1pbigwKS5tYXgoMTAwKS5vcHRpb25hbCgpLFxuICBub3RlczogSm9pLnN0cmluZygpLm9wdGlvbmFsKCksXG59KTtcblxuLy8gUGFnaW5hdGlvbiB2YWxpZGF0aW9uXG5leHBvcnQgY29uc3QgcGFnaW5hdGlvblNjaGVtYSA9IEpvaS5vYmplY3Qoe1xuICBwYWdlOiBKb2kubnVtYmVyKCkuaW50ZWdlcigpLm1pbigxKS5kZWZhdWx0KDEpLFxuICBsaW1pdDogSm9pLm51bWJlcigpLmludGVnZXIoKS5taW4oMSkubWF4KDEwMCkuZGVmYXVsdCgxMCksXG59KTtcblxuLy8gVmFsaWRhdGlvbiBoZWxwZXIgZnVuY3Rpb25cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZVJlcXVlc3Qoc2NoZW1hOiBKb2kuT2JqZWN0U2NoZW1hLCBkYXRhOiBhbnkpIHtcbiAgY29uc3QgeyBlcnJvciwgdmFsdWUgfSA9IHNjaGVtYS52YWxpZGF0ZShkYXRhLCB7IGFib3J0RWFybHk6IGZhbHNlIH0pO1xuXG4gIGlmIChlcnJvcikge1xuICAgIGNvbnN0IGVycm9ycyA9IGVycm9yLmRldGFpbHMubWFwKGRldGFpbCA9PiAoe1xuICAgICAgZmllbGQ6IGRldGFpbC5wYXRoLmpvaW4oJy4nKSxcbiAgICAgIG1lc3NhZ2U6IGRldGFpbC5tZXNzYWdlLFxuICAgIH0pKTtcblxuICAgIHJldHVybiB7XG4gICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgIGVycm9ycyxcbiAgICAgIGRhdGE6IG51bGwsXG4gICAgfTtcbiAgfVxuXG4gIHJldHVybiB7XG4gICAgaXNWYWxpZDogdHJ1ZSxcbiAgICBlcnJvcnM6IG51bGwsXG4gICAgZGF0YTogdmFsdWUsXG4gIH07XG59XG4iXSwibmFtZXMiOlsiSm9pIiwibG9naW5TY2hlbWEiLCJvYmplY3QiLCJpZGVudGlmaWVyIiwic3RyaW5nIiwicmVxdWlyZWQiLCJwYXNzd29yZCIsIm1pbiIsImxvZ2luX3R5cGUiLCJ2YWxpZCIsIm9wdGlvbmFsIiwibGVnYWN5TG9naW5TY2hlbWEiLCJlbWFpbCIsInJlZ2lzdGVyU2NoZW1hIiwidXNlcm5hbWUiLCJtYXgiLCJwYXR0ZXJuIiwiZnVsbF9uYW1lIiwicGhvbmUiLCJjcmVhdGVQcm9wZXJ0eVNjaGVtYSIsIm5hbWUiLCJ0eXBlIiwicGFyZW50X3Byb3BlcnR5X2lkIiwidXVpZCIsImFkZHJlc3MiLCJkZXNjcmlwdGlvbiIsImNhcGFjaXR5IiwibnVtYmVyIiwiaW50ZWdlciIsIndoZW4iLCJpcyIsInRoZW4iLCJvdGhlcndpc2UiLCJmb3JiaWRkZW4iLCJkZXBhcnRtZW50IiwicHJvamVjdF90eXBlIiwic3RhcnRfZGF0ZSIsImRhdGUiLCJleHBlY3RlZF9lbmRfZGF0ZSIsImhvdXJseV9yYXRlX3N0YW5kYXJkIiwicG9zaXRpdmUiLCJsb2NhdGlvbiIsImNyZWF0ZU1haW50ZW5hbmNlSXNzdWVTY2hlbWEiLCJwcm9wZXJ0eV9pZCIsInRpdGxlIiwicHJpb3JpdHkiLCJzZXJ2aWNlX3R5cGUiLCJkdWVfZGF0ZSIsInN1Ym1pdEF0dGVuZGFuY2VTY2hlbWEiLCJhdHRlbmRhbmNlIiwiYXJyYXkiLCJpdGVtcyIsIndvcmtlcl9pZCIsInN0YXR1cyIsImNoZWNrX2luX3RpbWUiLCJjaGVja19vdXRfdGltZSIsImhvdXJzX3dvcmtlZCIsIm5vdGVzIiwiY3JlYXRlRnVlbExvZ1NjaGVtYSIsImZ1ZWxfbGV2ZWxfbGl0ZXJzIiwiY29uc3VtcHRpb25fcmF0ZSIsInJ1bnRpbWVfaG91cnMiLCJlZmZpY2llbmN5X3BlcmNlbnRhZ2UiLCJwYWdpbmF0aW9uU2NoZW1hIiwicGFnZSIsImRlZmF1bHQiLCJsaW1pdCIsInZhbGlkYXRlUmVxdWVzdCIsInNjaGVtYSIsImRhdGEiLCJlcnJvciIsInZhbHVlIiwidmFsaWRhdGUiLCJhYm9ydEVhcmx5IiwiZXJyb3JzIiwiZGV0YWlscyIsIm1hcCIsImRldGFpbCIsImZpZWxkIiwicGF0aCIsImpvaW4iLCJtZXNzYWdlIiwiaXNWYWxpZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/validation.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/joi","vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froles%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();